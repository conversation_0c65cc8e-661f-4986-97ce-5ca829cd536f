/* ===================================
   متجر الشرق - CSS Framework
   Modern E-commerce Website Styles
   RTL Support for Arabic
   =================================== */

/* ===================================
   CSS Variables (Custom Properties)
   =================================== */
:root {
    /* Colors */
    --primary-color: #2c5aa0;
    --primary-dark: #1e3f73;
    --primary-light: #4a7bc8;
    --secondary-color: #ff6b35;
    --secondary-dark: #e55a2b;
    --secondary-light: #ff8c5a;

    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;

    --dark-color: #343a40;
    --light-color: #f8f9fa;
    --white-color: #ffffff;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;

    /* Typography */
    --font-family-primary: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-family-secondary: 'Amiri', 'Times New Roman', serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;

    /* Border Radius */
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.375rem;
    --border-radius-lg: 0.5rem;
    --border-radius-xl: 0.75rem;
    --border-radius-2xl: 1rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;

    /* Container */
    --container-max-width: 1200px;
    --container-padding: 1rem;
}

/* ===================================
   Base Styles
   =================================== */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--white-color);
    direction: rtl;
    text-align: right;
}

/* ===================================
   Typography
   =================================== */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-primary);
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
    color: var(--gray-900);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
    margin-bottom: var(--spacing-md);
    line-height: 1.7;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-dark);
}

/* ===================================
   Layout Components
   =================================== */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--container-padding);
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -0.75rem;
}

.col {
    flex: 1;
    padding: 0 0.75rem;
}

/* ===================================
   Buttons
   =================================== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    font-size: var(--font-size-base);
    font-weight: 600;
    text-align: center;
    text-decoration: none;
    border: 2px solid transparent;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    user-select: none;
    white-space: nowrap;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.25);
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    color: var(--white-color);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: var(--white-color);
    border-color: var(--secondary-color);
}

.btn-secondary:hover {
    background-color: var(--secondary-dark);
    border-color: var(--secondary-dark);
    color: var(--white-color);
}

.btn-outline {
    background-color: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: var(--white-color);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: var(--font-size-sm);
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: var(--font-size-lg);
}

/* ===================================
   Header Styles
   =================================== */
.header {
    background-color: var(--white-color);
    box-shadow: var(--shadow-md);
    position: sticky;
    top: 0;
    z-index: 1000;
}

/* Top Bar */
.top-bar {
    background-color: var(--gray-800);
    color: var(--white-color);
    padding: 0.5rem 0;
    font-size: var(--font-size-sm);
}

.top-bar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.top-bar-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.top-bar-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.top-link {
    color: var(--white-color);
    transition: color var(--transition-fast);
}

.top-link:hover {
    color: var(--primary-light);
}

.language-selector,
.currency-selector {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.language-selector select,
.currency-selector select {
    background-color: transparent;
    color: var(--white-color);
    border: 1px solid var(--gray-600);
    border-radius: var(--border-radius-sm);
    padding: 0.25rem 0.5rem;
    font-size: var(--font-size-xs);
}

/* Main Header */
.main-header {
    padding: var(--spacing-lg) 0;
}

.main-header .container {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.logo h1 {
    margin: 0;
    color: var(--primary-color);
    font-size: var(--font-size-2xl);
    font-weight: 700;
}

.logo i {
    margin-left: var(--spacing-sm);
    color: var(--secondary-color);
}

/* Search Container */
.search-container {
    flex: 1;
    max-width: 600px;
}

.search-bar {
    display: flex;
    border: 2px solid var(--gray-300);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    transition: border-color var(--transition-fast);
}

.search-bar:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
}

.category-select {
    background-color: var(--gray-100);
    border: none;
    padding: 0.75rem 1rem;
    font-size: var(--font-size-sm);
    color: var(--gray-700);
    min-width: 140px;
}

.search-input {
    flex: 1;
    border: none;
    padding: 0.75rem 1rem;
    font-size: var(--font-size-base);
    outline: none;
}

.search-btn {
    background-color: var(--primary-color);
    color: var(--white-color);
    border: none;
    padding: 0.75rem 1.5rem;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.search-btn:hover {
    background-color: var(--primary-dark);
}

/* User Actions */
.user-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.action-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--gray-700);
    position: relative;
    transition: color var(--transition-fast);
}

.action-link:hover {
    color: var(--primary-color);
}

.action-link i {
    font-size: var(--font-size-xl);
}

.action-link span {
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--secondary-color);
    color: var(--white-color);
    font-size: 0.7rem;
    font-weight: 700;
    padding: 0.2rem 0.4rem;
    border-radius: 50%;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Navigation Menu */
.main-nav {
    background-color: var(--primary-color);
    padding: 0;
}

.main-nav .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-menu li {
    position: relative;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 1rem 1.5rem;
    color: var(--white-color);
    font-weight: 600;
    transition: background-color var(--transition-fast);
}

.nav-link:hover,
.nav-link.active {
    background-color: var(--primary-dark);
    color: var(--white-color);
}

.nav-link i {
    font-size: var(--font-size-xs);
}

/* Dropdown Menu */
.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--white-color);
    box-shadow: var(--shadow-lg);
    border-radius: var(--border-radius-md);
    min-width: 200px;
    list-style: none;
    margin: 0;
    padding: 0.5rem 0;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
    z-index: 1000;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li a {
    display: block;
    padding: 0.75rem 1.5rem;
    color: var(--gray-700);
    transition: background-color var(--transition-fast);
}

.dropdown-menu li a:hover {
    background-color: var(--gray-100);
    color: var(--primary-color);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    color: var(--white-color);
    font-size: var(--font-size-xl);
    cursor: pointer;
    padding: 1rem;
}

/* ===================================
   Main Content Styles
   =================================== */
.main-content {
    min-height: calc(100vh - 200px);
}

.section-title {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
    color: var(--gray-900);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    right: 50%;
    transform: translateX(50%);
    width: 60px;
    height: 3px;
    background-color: var(--secondary-color);
    border-radius: 2px;
}

/* ===================================
   Hero Section
   =================================== */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white-color);
    padding: var(--spacing-3xl) 0;
    position: relative;
    overflow: hidden;
}

.hero-slider {
    position: relative;
}

.hero-slide {
    display: none;
    align-items: center;
    gap: var(--spacing-2xl);
}

.hero-slide.active {
    display: flex;
}

.hero-content {
    flex: 1;
}

.hero-content h2 {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-lg);
    color: var(--white-color);
}

.hero-content p {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xl);
    opacity: 0.9;
}

.hero-image {
    flex: 1;
    text-align: center;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    border-radius: var(--border-radius-lg);
}

.hero-controls {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 var(--spacing-lg);
}

.hero-prev,
.hero-next {
    background-color: rgba(255, 255, 255, 0.2);
    color: var(--white-color);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.hero-prev:hover,
.hero-next:hover {
    background-color: rgba(255, 255, 255, 0.3);
}

/* ===================================
   Categories Section
   =================================== */
.categories-section {
    padding: var(--spacing-3xl) 0;
    background-color: var(--gray-100);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-xl);
}

.category-card {
    background-color: var(--white-color);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    text-align: center;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    cursor: pointer;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.category-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
}

.category-icon i {
    font-size: var(--font-size-2xl);
    color: var(--white-color);
}

.category-card h3 {
    margin-bottom: var(--spacing-sm);
    color: var(--gray-900);
}

.category-card p {
    color: var(--gray-600);
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-sm);
}

.category-link {
    color: var(--primary-color);
    font-weight: 600;
    transition: color var(--transition-fast);
}

.category-link:hover {
    color: var(--primary-dark);
}

/* ===================================
   Products Section
   =================================== */
.featured-products {
    padding: var(--spacing-3xl) 0;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
}

.product-card {
    background-color: var(--white-color);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    position: relative;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.product-image {
    position: relative;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-badges {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.badge-new {
    background-color: var(--success-color);
    color: var(--white-color);
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.badge-sale {
    background-color: var(--danger-color);
    color: var(--white-color);
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.badge-hot {
    background-color: var(--warning-color);
    color: var(--gray-900);
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.product-actions {
    position: absolute;
    top: var(--spacing-md);
    left: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    opacity: 0;
    transform: translateX(-20px);
    transition: all var(--transition-normal);
}

.product-card:hover .product-actions {
    opacity: 1;
    transform: translateX(0);
}

.action-btn {
    width: 40px;
    height: 40px;
    background-color: var(--white-color);
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
}

.action-btn:hover {
    background-color: var(--primary-color);
    color: var(--white-color);
    transform: scale(1.1);
}

.product-info {
    padding: var(--spacing-lg);
}

.product-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--gray-900);
}

.product-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.stars {
    display: flex;
    gap: 2px;
}

.stars i {
    color: var(--warning-color);
    font-size: var(--font-size-sm);
}

.rating-count {
    font-size: var(--font-size-xs);
    color: var(--gray-600);
}

.product-price {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.current-price {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
}

.old-price {
    font-size: var(--font-size-base);
    color: var(--gray-500);
    text-decoration: line-through;
}

.add-to-cart {
    width: 100%;
}

.section-footer {
    text-align: center;
}

/* ===================================
   Special Offers Section
   =================================== */
.special-offers {
    padding: var(--spacing-3xl) 0;
    background-color: var(--gray-100);
}

.offers-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: var(--spacing-xl);
}

.offer-card {
    background-color: var(--white-color);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: transform var(--transition-normal);
}

.offer-card:hover {
    transform: translateY(-5px);
}

.offer-card.large {
    display: flex;
    align-items: center;
}

.offer-card.large .offer-content {
    flex: 1;
    padding: var(--spacing-2xl);
}

.offer-card.large .offer-image {
    flex: 1;
}

.offer-card:not(.large) .offer-content {
    padding: var(--spacing-xl);
    text-align: center;
}

.offer-content h3 {
    margin-bottom: var(--spacing-md);
    color: var(--gray-900);
}

.offer-content p {
    color: var(--gray-600);
    margin-bottom: var(--spacing-lg);
}

.countdown {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
    justify-content: center;
}

.countdown-item {
    text-align: center;
}

.countdown-number {
    display: block;
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-color);
}

.countdown-label {
    font-size: var(--font-size-xs);
    color: var(--gray-600);
}

.offer-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* ===================================
   Newsletter Section
   =================================== */
.newsletter {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white-color);
    padding: var(--spacing-2xl) 0;
}

.newsletter-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-2xl);
}

.newsletter-text h3 {
    color: var(--white-color);
    margin-bottom: var(--spacing-sm);
}

.newsletter-text p {
    opacity: 0.9;
    margin-bottom: 0;
}

.newsletter-form form {
    display: flex;
    gap: var(--spacing-md);
}

.newsletter-form input {
    padding: 0.75rem 1rem;
    border: none;
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    min-width: 300px;
}

.newsletter-form input:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}